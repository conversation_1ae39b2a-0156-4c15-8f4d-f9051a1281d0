# Capacitor Updater Implementation Guide

This document explains how to use the @capgo/capacitor-updater implementation for over-the-air updates.

## Overview

The app now supports automatic over-the-air updates using GitHub Releases as the distribution method. This allows you to push updates to users without going through the App Store or Play Store review process.

## How It Works

1. **Version Management**: App versions are managed in `package.json`
2. **Build & Release**: GitHub Actions automatically create releases with update bundles
3. **Update Detection**: App checks for updates on startup
4. **Download & Install**: Updates are downloaded and applied automatically
5. **Activation**: Updates take effect on next app restart

## Usage

### Creating a New Release

#### Method 1: Using the Script (Recommended)
```bash
# Create a patch release (1.0.0 → 1.0.1)
./scripts/create-release.sh patch

# Create a minor release (1.0.1 → 1.1.0)
./scripts/create-release.sh minor

# Create a major release (1.1.0 → 2.0.0)
./scripts/create-release.sh major

# With custom commit message
./scripts/create-release.sh patch "Fix critical bug in payment system"
```

#### Method 2: Manual Process
```bash
# 1. Update version
npm run version:patch  # or version:minor, version:major

# 2. Build and create bundle
npm run build:update

# 3. Commit and tag
git add package.json package-lock.json
git commit -m "Release v1.0.1"
git tag v1.0.1

# 4. Push
git push origin main
git push origin v1.0.1
```

#### Method 3: GitHub Actions Manual Trigger
1. Go to GitHub Actions tab
2. Select "Create Update Release" workflow
3. Click "Run workflow"
4. Enter the version number (e.g., 1.0.1)
5. Click "Run workflow"

### Monitoring Updates

#### Debug Panel
- **Development**: Automatically visible in dev mode
- **Production**: Add `?debug=true` to URL to show debug panel
- **Features**: 
  - View current version
  - Check for updates manually
  - View update logs
  - Force reload app

#### Console Logs
Check browser/device console for update-related logs:
```
UpdateService: Initialized with version 1.0.0
UpdateService: Checking for updates...
UpdateService: New version available: 1.0.1
UpdateService: Download complete, setting bundle
```

## Configuration

### Capacitor Config
Update frequency and behavior can be configured in `capacitor.config.ts`:

```typescript
CapacitorUpdater: {
  autoUpdate: true,           // Enable automatic updates
  checkDelay: 10000,         // Delay before first check (ms)
  directUpdate: true,        // Apply updates immediately
  resetWhenUpdate: true,     // Reset app state on update
  updateAvailable: true,     // Show update available events
}
```

### GitHub Repository
Make sure your repository is public or configure proper access tokens for private repos.

## File Structure

```
├── src/services/updateService.ts     # Main update service
├── src/components/UpdateDebugPanel.vue  # Debug component
├── scripts/create-release.sh         # Release automation script
├── .github/workflows/release-update.yml  # GitHub Actions workflow
└── docs/CAPACITOR_UPDATER.md        # This documentation
```

## Troubleshooting

### Common Issues

1. **Updates not detected**
   - Check GitHub repository URL in `capacitor.config.ts`
   - Verify release was created successfully
   - Check console logs for errors

2. **Download fails**
   - Ensure update bundle exists in release assets
   - Check network connectivity
   - Verify bundle format (should be .zip)

3. **Update doesn't apply**
   - Check if app restarted after download
   - Look for bundle setting errors in logs
   - Verify bundle integrity

### Debug Steps

1. **Enable debug panel**: Add `?debug=true` to URL
2. **Check console logs**: Look for UpdateService messages
3. **Verify release**: Check GitHub releases page
4. **Test manually**: Use "Check for Updates" button in debug panel

## Best Practices

### Version Management
- Use semantic versioning (MAJOR.MINOR.PATCH)
- Increment patch for bug fixes
- Increment minor for new features
- Increment major for breaking changes

### Release Process
- Test updates thoroughly before release
- Use descriptive commit messages
- Monitor update adoption rates
- Keep release notes updated

### Security
- Only include necessary files in update bundles
- Verify bundle integrity with checksums
- Monitor for unauthorized releases

## Limitations

### What CAN be updated:
- JavaScript/TypeScript code
- CSS/SCSS styles
- HTML templates
- Images and assets
- Configuration files

### What CANNOT be updated:
- Native plugins
- Capacitor configuration
- App permissions
- Native code changes

For native changes, you still need to release through app stores.

## Support

For issues related to:
- **Capacitor Updater**: Check [official documentation](https://github.com/Cap-go/capacitor-updater)
- **GitHub Actions**: Check workflow logs in Actions tab
- **App-specific issues**: Check console logs and debug panel
