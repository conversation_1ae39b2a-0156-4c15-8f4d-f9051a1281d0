PODS:
  - Capacitor (7.0.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (7.0.0):
    - Capacitor
  - CapacitorCamera (7.0.1):
    - Capacitor
  - CapacitorClipboard (7.0.1):
    - Capacitor
  - CapacitorCordova (7.0.0)
  - CapacitorHaptics (7.0.0):
    - Capacitor
  - CapacitorKeyboard (7.0.0):
    - Capacitor
  - CapacitorShare (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.0):
    - Capacitor
  - CordovaPluginsStatic (7.2.0):
    - CapacitorCordova
    - OneSignalXCFramework (= 5.2.10)
  - OneSignalXCFramework (5.2.10):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.10)
  - OneSignalXCFramework/OneSignal (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.10):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.10)
  - OneSignalXCFramework/OneSignalExtension (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.10):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.10):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorClipboard (from `../../node_modules/@capacitor/clipboard`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorShare (from `../../node_modules/@capacitor/share`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - CordovaPluginsStatic (from `../capacitor-cordova-ios-plugins`)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)

SPEC REPOS:
  trunk:
    - OneSignalXCFramework

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorClipboard:
    :path: "../../node_modules/@capacitor/clipboard"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorShare:
    :path: "../../node_modules/@capacitor/share"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CordovaPluginsStatic:
    :path: "../capacitor-cordova-ios-plugins"

SPEC CHECKSUMS:
  Capacitor: 82d1f3b4480d66b5996814f74500dcbc0908558c
  CapacitorApp: 9cb31064a6c6bb2b1438583733a7bf45557fc1da
  CapacitorCamera: 6e73f1fc6c629a672658705a02409b60854bc0f1
  CapacitorClipboard: 70bfdb42b877b320a6e511ab94fa7a6a55d57ecb
  CapacitorCordova: 345f93b7edd121db98e4ec20ac94d6d7bcaf7e48
  CapacitorHaptics: 7be406a91e4eb87287f321c6c68e1709d6837b3a
  CapacitorKeyboard: 4db71e694e7afb5d7c0be09b05495c19f7d6c914
  CapacitorShare: e573823f511f260f598d0423c33b1e3d7bbe5fd1
  CapacitorStatusBar: a8c4c83ed2e973bdafb979e80e4b00d027832cb7
  CordovaPluginsStatic: d39f5abacfc5a78150f4d0005ffa6ae9a55725d2
  OneSignalXCFramework: 1a3b28dfbff23aabce585796d23c1bef37772774

PODFILE CHECKSUM: e9ed1bf32ffd76ad0aeea2336e5135a49f98678a

COCOAPODS: 1.16.2
