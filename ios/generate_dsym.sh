#!/bin/bash

# Find the most recent archive
ARCHIVE_PATH=$(find ~/Library/Developer/Xcode/Archives -name "*.xcarchive" -type d -print0 | xargs -0 ls -td | head -n 1)

if [ -z "$ARCHIVE_PATH" ]; then
  echo "No archives found"
  exit 1
fi

echo "Using archive: $ARCHIVE_PATH"

# Find the IonicLiveUpdates.framework in the archive
FRAMEWORK_PATH=$(find "$ARCHIVE_PATH" -name "IonicLiveUpdates.framework" -type d | head -n 1)

if [ -z "$FRAMEWORK_PATH" ]; then
  echo "IonicLiveUpdates.framework not found in archive"
  exit 1
fi

echo "Found framework at: $FRAMEWORK_PATH"

# Get the binary path
BINARY_PATH="$FRAMEWORK_PATH/IonicLiveUpdates"

if [ ! -f "$BINARY_PATH" ]; then
  echo "Binary not found at: $BINARY_PATH"
  exit 1
fi

echo "Found binary at: $BINARY_PATH"

# Create the dSYM directory if it doesn't exist
DSYM_DIR="$ARCHIVE_PATH/dSYMs"
mkdir -p "$DSYM_DIR"

# Generate the dSYM file
DSYM_PATH="$DSYM_DIR/IonicLiveUpdates.framework.dSYM"
echo "Generating dSYM at: $DSYM_PATH"

xcrun dsymutil -o "$DSYM_PATH" "$BINARY_PATH"

if [ $? -eq 0 ]; then
  echo "Successfully generated dSYM file"
  
  # Get the UUID of the binary
  UUID=$(xcrun dwarfdump --uuid "$BINARY_PATH" | awk '{print $2}')
  echo "Binary UUID: $UUID"
  
  # Get the UUID of the dSYM
  DSYM_UUID=$(xcrun dwarfdump --uuid "$DSYM_PATH" | awk '{print $2}')
  echo "dSYM UUID: $DSYM_UUID"
  
  if [ "$UUID" = "$DSYM_UUID" ]; then
    echo "UUIDs match. dSYM file should be valid."
  else
    echo "Warning: UUIDs do not match."
  fi
else
  echo "Failed to generate dSYM file"
fi
