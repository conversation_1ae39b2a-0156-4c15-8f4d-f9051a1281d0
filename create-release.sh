#!/bin/bash

# Create Release Script for Capacitor Updater
# Usage: ./scripts/create-release.sh [patch|minor|major] [optional-message]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
VERSION_TYPE="patch"
COMMIT_MESSAGE=""

# Parse arguments
if [ $# -ge 1 ]; then
    VERSION_TYPE=$1
fi

if [ $# -ge 2 ]; then
    COMMIT_MESSAGE=$2
fi

echo -e "${GREEN}🚀 Creating new release...${NC}"

# Validate version type
if [[ ! "$VERSION_TYPE" =~ ^(patch|minor|major)$ ]]; then
    echo -e "${RED}❌ Invalid version type. Use: patch, minor, or major${NC}"
    exit 1
fi

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ Not in a git repository${NC}"
    exit 1
fi

# Check if working directory is clean
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}⚠️  Working directory is not clean. Uncommitted changes:${NC}"
    git status --short
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}❌ Aborted${NC}"
        exit 1
    fi
fi

# Get current version
CURRENT_VERSION=$(node -p "require('./package.json').version")
echo -e "${YELLOW}📋 Current version: ${CURRENT_VERSION}${NC}"

# Update version
echo -e "${GREEN}📝 Updating version (${VERSION_TYPE})...${NC}"
npm run version:${VERSION_TYPE}

# Get new version
NEW_VERSION=$(node -p "require('./package.json').version")
echo -e "${GREEN}✅ New version: ${NEW_VERSION}${NC}"

# Build the project
echo -e "${GREEN}🔨 Building project...${NC}"
npm run build

# Create update bundle
echo -e "${GREEN}📦 Creating update bundle...${NC}"
npm run create-update-bundle

# Commit changes
if [ -z "$COMMIT_MESSAGE" ]; then
    COMMIT_MESSAGE="Release v${NEW_VERSION}"
fi

echo -e "${GREEN}💾 Committing changes...${NC}"
git add package.json package-lock.json
git commit -m "$COMMIT_MESSAGE"

# Create and push tag
echo -e "${GREEN}🏷️  Creating tag v${NEW_VERSION}...${NC}"
git tag "v${NEW_VERSION}"

echo -e "${GREEN}⬆️  Pushing changes and tag...${NC}"
git push origin main
git push origin "v${NEW_VERSION}"

echo -e "${GREEN}✅ Release v${NEW_VERSION} created successfully!${NC}"
echo -e "${YELLOW}📱 The GitHub Action will automatically create the release with update bundle.${NC}"
echo -e "${YELLOW}🔄 Mobile apps will check for this update on next startup.${NC}"

# Show next steps
echo -e "\n${GREEN}📋 Next steps:${NC}"
echo -e "1. Check GitHub Actions: https://github.com/mlolpet/syner-biz/actions"
echo -e "2. Verify release created: https://github.com/mlolpet/syner-biz/releases"
echo -e "3. Test update on mobile device"
echo -e "4. Monitor app logs for update process"
