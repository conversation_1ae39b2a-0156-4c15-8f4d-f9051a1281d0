import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.syner.biz',
  //appName: '商聯思維',
  appName: 'Synerthink',
  webDir: 'dist',
  ios: {
    handleApplicationNotifications: false
  },
  plugins: {
    Camera: {
      promptBeforeEnabling: true,
      promptMessage: "請允許使用相機以掃描QR碼"
    },
    CapacitorUpdater: {
      autoUpdate: true,
      updateUrl: 'https://api.github.com/repos/mlolpet/syner-biz/releases/latest',
      statsUrl: '',
      privateKey: '',
      version: '',
      directUpdate: true,
      resetWhenUpdate: true,
      updateAvailable: true,
      checkDelay: 10000,
      localS3: false,
      localHost: '',
      localWebHost: 'localhost',
      localSupa: '',
      allowModifyUrl: true,
      defaultChannel: 'production'
    }
  }
};

export default config;
