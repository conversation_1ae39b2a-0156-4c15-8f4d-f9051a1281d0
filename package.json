{"name": "syner-biz", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@amcharts/amcharts5": "^5.12.1", "@capacitor/android": "^7.0.0", "@capacitor/app": "7.0.0", "@capacitor/camera": "^7.0.1", "@capacitor/clipboard": "^7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/keyboard": "7.0.0", "@capacitor/share": "^7.0.1", "@capacitor/status-bar": "7.0.0", "@capawesome/capacitor-android-edge-to-edge-support": "^7.1.1", "@ionic/pwa-elements": "^3.3.0", "@ionic/vue": "^8.0.0", "@ionic/vue-router": "^8.0.0", "@schedule-x/calendar": "^1.14.0", "@schedule-x/calendar-controls": "^2.25.0", "@schedule-x/theme-default": "^1.14.0", "@schedule-x/theme-shadcn": "^2.25.0", "@schedule-x/vue": "^1.14.0", "@supabase/supabase-js": "^2.49.4", "@vuepic/vue-datepicker": "^7.4.1", "@vueuse/core": "^10.9.0", "compressorjs": "^1.2.1", "html5-qrcode": "^2.3.8", "ionicons": "^7.2.2", "libphonenumber-js": "^1.10.58", "moment": "^2.30.1", "onesignal-cordova-plugin": "^5.2.11", "pinia": "^2.1.7", "quill": "^2.0.3", "quill-resize-module": "^2.0.4", "swiper": "^11.0.7", "vue": "^3.4.38", "vue-router": "^4.3.0", "vue-star-rating": "^2.1.0", "zod": "^3.22.4"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "7.2.0", "@types/node": "^20.11.24", "@vitejs/plugin-vue": "^5.1.3", "sass": "npm:sass-embedded@^1.89.0", "typescript": "^5.5.3", "vite": "^5.4.2", "vite-plugin-pwa": "^0.19.2", "vue-tsc": "^2.1.4"}}